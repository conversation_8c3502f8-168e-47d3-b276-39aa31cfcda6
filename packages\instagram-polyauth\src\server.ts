import { BetterAuthPlugin } from "better-auth";
import { genericOAuth } from "better-auth/plugins";
import { OAuthProvider } from "better-auth/oauth2";

export interface InstagramPolyAuthOptions {
  clientId: string;
  clientSecret: string;
  // Additional client IDs to accept for token validation
  additionalClientIds?: string[];
  scopes?: string[];
  redirectURI?: string;
}

/**
 * A plugin that extends Instagram OAuth to support multiple client IDs
 * for token validation, which is especially useful for mobile applications.
 */
export const instagramPolyAuth = (options: InstagramPolyAuthOptions): BetterAuthPlugin => {
  return {
    id: "instagram-polyauth",

    init: (ctx) => {
      ctx.logger?.debug?.("Initializing instagram-polyauth plugin", {
        primaryClientId: options.clientId,
        additionalClientIdsCount: options.additionalClientIds?.length || 0,
      });

      // Create the Instagram provider using genericOAuth
      const instagramProvider = {
        id: "instagram",
        name: "Instagram",
        type: "oauth2" as const,
        clientId: options.clientId,
        clientSecret: options.clientSecret,
        authorizationUrl: "https://api.instagram.com/oauth/authorize",
        tokenUrl: "https://api.instagram.com/oauth/access_token",
        userInfoUrl: "https://graph.instagram.com/me",
        scopes: options.scopes || ["user_profile", "user_media"],
        redirectURI: options.redirectURI,
        
        // Custom user info fetching for Instagram
        getUserInfo: async (tokens: any) => {
          try {
            const response = await fetch(
              `https://graph.instagram.com/me?fields=id,username,account_type&access_token=${tokens.accessToken}`
            );
            
            if (!response.ok) {
              ctx.logger?.error?.("Failed to fetch Instagram user info");
              return null;
            }
            
            const userInfo = await response.json();
            
            return {
              id: userInfo.id,
              name: userInfo.username,
              email: null, // Instagram doesn't provide email
              image: null, // Would need additional API call for profile picture
            };
          } catch (error) {
            ctx.logger?.error?.("Instagram user info fetch error", error);
            return null;
          }
        },

        // Custom token validation for multiple client IDs
        verifyIdToken: async (token: string, nonce?: string) => {
          try {
            // For Instagram, we'll validate the access token instead of ID token
            // since Instagram uses OAuth 2.0, not OpenID Connect
            const response = await fetch(
              `https://graph.instagram.com/me?access_token=${token}`
            );

            if (!response.ok) {
              // If primary validation fails, try with additional client IDs
              if (options.additionalClientIds?.length) {
                // Instagram doesn't have a direct token info endpoint like Google
                // We'll need to validate through the user info endpoint
                for (const clientId of options.additionalClientIds) {
                  try {
                    // This is a simplified validation - in practice, you might need
                    // to store and validate tokens differently for Instagram
                    const altResponse = await fetch(
                      `https://graph.instagram.com/me?access_token=${token}`
                    );
                    
                    if (altResponse.ok) {
                      ctx.logger?.debug?.("Validated Instagram token with additional client ID", {
                        clientId,
                      });
                      return true;
                    }
                  } catch (error) {
                    ctx.logger?.debug?.("Instagram token validation failed for additional client ID", {
                      clientId,
                      error,
                    });
                  }
                }
              }
              
              ctx.logger?.debug?.("Instagram token validation failed");
              return false;
            }

            ctx.logger?.debug?.("Validated Instagram token with primary client ID");
            return true;
          } catch (error) {
            ctx.logger?.error?.("Instagram token verification error", error);
            return false;
          }
        },
      } as OAuthProvider<any>;

      // Get existing socialProviders if available or create a new array
      const existingSocialProviders: OAuthProvider<any>[] = (ctx as any).socialProviders || [];

      // Check if there's already an Instagram provider
      const existingInstagramProviderIndex = existingSocialProviders.findIndex(
        (provider: OAuthProvider<any>) => provider.id === "instagram"
      );

      // Create a new array to avoid modifying the original
      const socialProviders = [...existingSocialProviders];

      // If an Instagram provider already exists, replace it; otherwise, add our new one
      if (existingInstagramProviderIndex !== -1) {
        ctx.logger?.debug?.(
          "Replacing existing Instagram provider with enhanced multi-client version"
        );
        socialProviders[existingInstagramProviderIndex] = instagramProvider;
      } else {
        ctx.logger?.debug?.("Adding Instagram polyauth provider");
        socialProviders.push(instagramProvider);
      }

      // Return the context with our modified social providers
      return {
        context: {
          socialProviders,
        },
      };
    },
  };
};
