import { createAuthClient } from "better-auth/react";
import {
  passkeyClient,
  adminClient,
  customSessionClient,
  inferAdditionalFields,
} from "better-auth/client/plugins";
import { auth } from "@/lib/auth";
import { toast } from "sonner";

export const client = createAuthClient({
  baseURL: typeof window !== 'undefined'
    ? window.location.origin
    : process.env.VITE_APP_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  plugins: [
    passkeyClient(),
    adminClient(),
    customSessionClient<typeof auth>(),
    inferAdditionalFields<typeof auth>(),
  ],
  fetchOptions: {
    onError(e) {
      if (e.error.status === 429) {
        toast.error("Too many attempts. Please try again later.");
      }
      // Redirect to login page for authentication errors
      if (e.error.status === 401 || e.error.status === 403) {
        window.location.href = `/login?error=unable_to_create_user`;
      }
    },
  },
});

export const { signIn, signOut, useSession, passkey } = client;

// Export fetch client for authenticated requests
export const $fetch = client.$fetch;
